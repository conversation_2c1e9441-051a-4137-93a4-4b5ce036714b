# 模块懒加载解决方案

## 概述

为了解决页面图片资源过多影响性能的问题，我设计了一个简单易用的模块懒加载方案。该方案具有以下特点：

- ✅ **视口检测**：只有当模块进入视口时才加载
- ✅ **骨架屏支持**：加载过程中显示骨架屏，提升用户体验
- ✅ **顺序加载**：可控制模块按顺序加载，避免同时加载多个模块
- ✅ **简单易用**：只需包装现有组件即可
- ✅ **性能优化**：减少初始页面加载时间

## 实现的组件

### 1. LazyModule.vue
主要的懒加载容器组件，负责：
- 视口检测（使用 Intersection Observer API）
- 控制加载时机
- 显示/隐藏骨架屏
- 触发加载事件

### 2. HomeSkeleton.vue
专门为首页设计的骨架屏组件，支持：
- 瀑布流骨架屏（waterfall）
- 横向滚动骨架屏（horizontal）
- 列表骨架屏（list）

## 使用方法

### 基本用法

```vue
<LazyModule 
  module-id="my-module" 
  @load-start="handleLoadStart">
  
  <template #skeleton>
    <HomeSkeleton type="waterfall" :count="4" title="模块标题" />
  </template>
  
  <!-- 你的模块内容 -->
  <Block title="商品列表">
    <!-- 内容 -->
  </Block>
</LazyModule>
```

### 在首页中的应用

已经将首页的三个主要模块改造为懒加载：

1. **各县销冠**：进入视口立即加载（`wait-for-previous="false"`）
2. **新上好物**：等待上一个模块完成后加载（`wait-for-previous="true"`）
3. **爆款好物**：等待上一个模块完成后加载（`wait-for-previous="true"`）

## 核心特性

### 1. 视口检测
使用 Intersection Observer API 检测模块是否进入视口：
- 提前100px开始检测（可配置）
- 避免重复触发
- 性能优化

### 2. 骨架屏
提供三种类型的骨架屏：
- **瀑布流**：适用于商品网格布局
- **横向滚动**：适用于横向滚动的商品列表
- **列表**：适用于垂直列表布局

### 3. 顺序加载
通过 `wait-for-previous` 属性控制：
- `false`：立即加载（适用于重要模块）
- `true`：等待上一个模块完成（避免同时加载多个模块）

## 性能优化效果

### 优化前
- 页面加载时同时请求所有模块数据
- 大量图片同时加载，影响首屏性能
- 用户可能看不到的内容也会加载

### 优化后
- 只加载用户看到的模块
- 按需加载，减少初始请求
- 骨架屏提升用户体验
- 顺序加载避免网络拥塞

## 文件结构

```
src/
├── components/
│   └── Common/
│       ├── LazyModule.vue          # 主要懒加载组件
│       └── Home/
│           └── HomeSkeleton.vue    # 骨架屏组件
├── views/
│   ├── Home/BFHome/
│   │   └── IndexView.vue          # 已改造的首页
│   └── Demo/
│       └── LazyModuleDemo.vue     # 演示页面
└── docs/
    └── LazyModule使用说明.md       # 详细使用文档
```

## 演示页面

创建了 `LazyModuleDemo.vue` 演示页面，展示：
- 不同类型的骨架屏效果
- 顺序加载的过程
- 实时加载状态显示

## 最佳实践

1. **重要模块优先**：首屏重要内容设置 `wait-for-previous="false"`
2. **合理的骨架屏**：选择与实际内容相似的骨架屏类型
3. **适当的延迟**：让骨架屏显示足够时间，避免闪烁
4. **监听加载事件**：在 `load-start` 事件中执行数据加载逻辑

## 兼容性

- 支持现代浏览器（IE11+）
- 使用 Intersection Observer API（有 polyfill 支持）
- 与现有组件完全兼容

## 扩展性

该方案设计简单，易于扩展：
- 可以添加更多骨架屏类型
- 可以自定义加载策略
- 可以集成到其他页面

这个解决方案完全满足您的需求：简单、易用、性能优化，并且配合骨架屏提升用户体验。
