<template>
  <div class="lazy-demo">
    <div class="demo-header">
      <h1>懒加载模块演示</h1>
      <p>向下滚动查看模块懒加载效果</p>
    </div>

    <!-- 占位内容，让页面有足够高度 -->
    <div class="placeholder-content">
      <div class="content-block" v-for="i in 3" :key="i">
        <h3>页面内容 {{ i }}</h3>
        <p>这是一些页面内容，用来演示懒加载效果。继续向下滚动查看懒加载模块。</p>
      </div>
    </div>

    <!-- 懒加载模块1：瀑布流 -->
    <LazyModule 
      module-id="demo-waterfall" 
      :wait-for-previous="false"
      @load-start="loadWaterfallData"
      @load-complete="onWaterfallComplete">
      <template #skeleton>
        <HomeSkeleton type="waterfall" :count="4" title="瀑布流模块" />
      </template>
      
      <div class="demo-module">
        <h2>瀑布流模块</h2>
        <div class="waterfall-grid">
          <div v-for="item in waterfallData" :key="item.id" class="waterfall-item">
            <div class="item-image" :style="{ backgroundColor: item.color }">
              <span>{{ item.id }}</span>
            </div>
            <div class="item-content">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </LazyModule>

    <!-- 懒加载模块2：横向滚动 -->
    <LazyModule 
      module-id="demo-horizontal" 
      :wait-for-previous="true"
      @load-start="loadHorizontalData"
      @load-complete="onHorizontalComplete">
      <template #skeleton>
        <HomeSkeleton type="horizontal" :count="5" title="横向滚动模块" />
      </template>
      
      <div class="demo-module">
        <h2>横向滚动模块</h2>
        <div class="horizontal-scroll">
          <div v-for="item in horizontalData" :key="item.id" class="horizontal-item">
            <div class="item-image" :style="{ backgroundColor: item.color }">
              <span>{{ item.id }}</span>
            </div>
            <div class="item-content">
              <h4>{{ item.title }}</h4>
            </div>
          </div>
        </div>
      </div>
    </LazyModule>

    <!-- 懒加载模块3：列表 -->
    <LazyModule 
      module-id="demo-list" 
      :wait-for-previous="true"
      @load-start="loadListData"
      @load-complete="onListComplete">
      <template #skeleton>
        <HomeSkeleton type="list" :count="3" title="列表模块" />
      </template>
      
      <div class="demo-module">
        <h2>列表模块</h2>
        <div class="list-container">
          <div v-for="item in listData" :key="item.id" class="list-item">
            <div class="item-image" :style="{ backgroundColor: item.color }">
              <span>{{ item.id }}</span>
            </div>
            <div class="item-content">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </LazyModule>

    <!-- 加载状态显示 -->
    <div class="load-status">
      <h3>加载状态</h3>
      <div class="status-list">
        <div class="status-item" :class="{ loaded: waterfallLoaded }">
          瀑布流模块: {{ waterfallLoaded ? '已加载' : '未加载' }}
        </div>
        <div class="status-item" :class="{ loaded: horizontalLoaded }">
          横向滚动模块: {{ horizontalLoaded ? '已加载' : '未加载' }}
        </div>
        <div class="status-item" :class="{ loaded: listLoaded }">
          列表模块: {{ listLoaded ? '已加载' : '未加载' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LazyModule from '@/components/Common/LazyModule.vue'
import HomeSkeleton from '@/components/Common/Home/HomeSkeleton.vue'

// 数据状态
const waterfallData = ref([])
const horizontalData = ref([])
const listData = ref([])

// 加载状态
const waterfallLoaded = ref(false)
const horizontalLoaded = ref(false)
const listLoaded = ref(false)

// 生成随机颜色
const getRandomColor = () => {
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
  return colors[Math.floor(Math.random() * colors.length)]
}

// 模拟数据加载
const loadWaterfallData = async () => {
  console.log('开始加载瀑布流数据...')
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  waterfallData.value = Array.from({ length: 6 }, (_, i) => ({
    id: i + 1,
    title: `瀑布流项目 ${i + 1}`,
    description: `这是瀑布流项目 ${i + 1} 的描述`,
    color: getRandomColor()
  }))
}

const loadHorizontalData = async () => {
  console.log('开始加载横向滚动数据...')
  
  await new Promise(resolve => setTimeout(resolve, 800))
  
  horizontalData.value = Array.from({ length: 8 }, (_, i) => ({
    id: i + 1,
    title: `横向项目 ${i + 1}`,
    color: getRandomColor()
  }))
}

const loadListData = async () => {
  console.log('开始加载列表数据...')
  
  await new Promise(resolve => setTimeout(resolve, 600))
  
  listData.value = Array.from({ length: 4 }, (_, i) => ({
    id: i + 1,
    title: `列表项目 ${i + 1}`,
    description: `这是列表项目 ${i + 1} 的详细描述`,
    color: getRandomColor()
  }))
}

// 加载完成回调
const onWaterfallComplete = () => {
  waterfallLoaded.value = true
  console.log('瀑布流模块加载完成')
}

const onHorizontalComplete = () => {
  horizontalLoaded.value = true
  console.log('横向滚动模块加载完成')
}

const onListComplete = () => {
  listLoaded.value = true
  console.log('列表模块加载完成')
}
</script>

<style scoped lang="less">
.lazy-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      color: #333;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
    }
  }

  .placeholder-content {
    margin-bottom: 40px;
    
    .content-block {
      background: #f8f9fa;
      padding: 20px;
      margin-bottom: 20px;
      border-radius: 8px;
      
      h3 {
        color: #333;
        margin-bottom: 10px;
      }
      
      p {
        color: #666;
        line-height: 1.6;
      }
    }
  }

  .demo-module {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h2 {
      color: #333;
      margin-bottom: 20px;
      text-align: center;
    }
  }

  .waterfall-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }

  .waterfall-item {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    
    .item-image {
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 18px;
    }
    
    .item-content {
      padding: 12px;
      
      h4 {
        margin-bottom: 8px;
        color: #333;
      }
      
      p {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .horizontal-scroll {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 10px;
    
    &::-webkit-scrollbar {
      height: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
  }

  .horizontal-item {
    flex: 0 0 160px;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    
    .item-image {
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
    }
    
    .item-content {
      padding: 12px;
      
      h4 {
        color: #333;
        font-size: 14px;
      }
    }
  }

  .list-container {
    .list-item {
      display: flex;
      background: #f8f9fa;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;
      
      .item-image {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        flex-shrink: 0;
      }
      
      .item-content {
        flex: 1;
        padding: 12px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        h4 {
          margin-bottom: 8px;
          color: #333;
        }
        
        p {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  .load-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    
    h3 {
      margin-bottom: 12px;
      color: #333;
      font-size: 16px;
    }
    
    .status-item {
      padding: 4px 0;
      color: #999;
      
      &.loaded {
        color: #52c41a;
        font-weight: bold;
      }
    }
  }
}
</style>
