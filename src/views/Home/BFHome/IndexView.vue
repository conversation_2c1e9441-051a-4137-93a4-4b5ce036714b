<template>
  <div class="bf-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <!-- Banner轮播 -->
    <div class="banner-container" v-if="headerBannerList.length > 0">
      <GoodsImageSwiper :media-list="headerBannerList" mode="banner" paginationType="fraction" :autoplay="true"
        :loop="true" height="200px" @image-click="handleBannerClick" />
    </div>

    <!-- 宫格菜单 -->
    <div class="grid-menu-container" v-if="gridMenuItems.length > 0">
      <GridMenu :items="gridMenuItems" :columns="5" :show-more="true" :max-items="10" @item-click="handleGridItemClick"
        @more-click="handleMoreClick" />
    </div>

    <Block title="各县销冠">
      <van-list :loading="limitedLoading" :finished="limitedFinished" finished-text="没有更多了" :offset="100"
        @load="handleLimitedLoadMore">
        <Waterfall v-if="limitedList.length > 0" :list="limitedList" :breakpoints="breakpoints"
          :hasAroundGutter="false">
          <template #default="{ item }">
            <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
          </template>
        </Waterfall>
      </van-list>
    </Block>

    <Block title="新上好物">
      <div class="horizontal-scroll-container" v-if="newerList.length > 0">
        <div class="horizontal-scroll-wrapper">
          <div class="goods-item" v-for="item in newerList" :key="item.goodsId" @click="handleGoodsClick(item)">
            <GoodsCard :goods-info="item" @click="handleGoodsClick(item)"/>
          </div>
        </div>
      </div>
    </Block>

    <Block title="爆款好物">
      <van-list :loading="hotProductsLoading" :finished="hotProductsFinished" finished-text="没有更多了" :offset="100"
        @load="handleHotProductsLoadMore">
        <Waterfall v-if="hotProductsList.length > 0" :list="hotProductsList" :breakpoints="breakpoints"
          :hasAroundGutter="false">
          <template #default="{ item }">
            <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
          </template>
        </Waterfall>
      </van-list>
    </Block>
  </div>
</template>
<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'
import GridMenu from '@components/Common/Home/GridMenu.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import Block from '@components/Common/Home/Block.vue'
import GoodsCard from '@components/Common/Home/GoodsCard.vue'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
const router = useRouter()
// 基础数据
const searchKeyword = ref('')
const headerBannerList = ref([])

// 10宫格菜单数据
const gridMenuItems = ref([])

// 各县销冠数据
const limitedList = ref([])
const limitedLoading = ref(false)
const limitedFinished = ref(false)
const limitedCurrentPage = ref(1)
const limitedPageSize = ref(10)
const limitedLastLoadTime = ref(0)
const limitedIsFirstLoadComplete = ref(false)

// 新上好物数据
const newerList = ref([])

// 爆款好物数据
const hotProductsList = ref([])
const hotProductsLoading = ref(false)
const hotProductsFinished = ref(false)
const hotProductsCurrentPage = ref(1)
const hotProductsPageSize = ref(10)
const hotProductsLastLoadTime = ref(0)
const hotProductsIsFirstLoadComplete = ref(false)

// 瀑布流配置
const breakpoints = ref({
  750: { rowPerView: 2 },
  550: { rowPerView: 2 },
  375: { rowPerView: 2 },
  290: { rowPerView: 1 }
})

// banner 过滤对应渠道的数据
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}

// 获取头部banner列表
const getHeaderBannerList = async () => {
  const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
  console.warn(213132, err, json)
  if (!err) {
    // 转换数据格式为GoodsImageSwiper组件需要的格式
    const bannerData = channelFilterd(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
    console.warn(1231323123, bannerData)
    headerBannerList.value = bannerData
  }
}

// 获取宫格菜单列表
const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 2
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData.slice(0, 4)
    } else {
      gridMenuItems.value = []
    }
  }

}

// 搜索处理
const handleSearch = () => {
  // 搜索功能由 SearchHeader 组件处理
}

// Banner点击处理
const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    // 处理banner点击跳转逻辑
    window.location.href = item.linkUrl
  }
}

// 宫格菜单点击处理
const handleGridItemClick = ({ item, index }) => {
  console.log('点击宫格菜单:', item, index)
  if (item.url) {
    // 这里可以使用 Vue Router 进行路由跳转
    // router.push(item.url)
    // 或者直接跳转
    window.location.href = item.url
  }
}

// 更多按钮点击处理
const handleMoreClick = () => {
  console.log('点击更多按钮')
  // 跳转到分类页面或显示更多菜单
  // router.push('/category')
}

// 获取各县销冠列表
const getLimitedList = async (isLoadMore = false) => {
  if (limitedLoading.value) return

  limitedLoading.value = true

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: limitedCurrentPage.value,
    page_size: limitedPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    if (isLoadMore) {
      limitedList.value = [...limitedList.value, ...newItems]
    } else {
      limitedList.value = newItems
    }

    // 只有当返回的数据为空数组时才停止加载
    if (json.length === 0) {
      limitedFinished.value = true
    }

    // 只有在成功加载数据时才递增页码
    if (isLoadMore) {
      limitedCurrentPage.value++
    } else {
      // 首次加载成功后，将页码设置为2，为下次加载做准备
      limitedCurrentPage.value = 2
    }

    await nextTick()

    // 给Waterfall更多时间来渲染和计算布局，特别是首次加载
    if (!isLoadMore) {
      // 首次加载后额外等待，确保瀑布流完全渲染
      await new Promise(resolve => setTimeout(resolve, 500))
      limitedIsFirstLoadComplete.value = true
    } else {
      // 后续加载稍微等待一下
      await new Promise(resolve => setTimeout(resolve, 200))
    }

  } else {
    // 没有数据或出错时，标记为加载完成
    limitedFinished.value = true
  }

  limitedLoading.value = false
}
// 各县销冠加载更多处理
const handleLimitedLoadMore = () => {
  const now = Date.now()
  if (!limitedFinished.value && !limitedLoading.value && limitedIsFirstLoadComplete.value && (now - limitedLastLoadTime.value > 1000)) {
    limitedLastLoadTime.value = now
    getLimitedList(true)
  }
}

// 获取新上好物列表
const getNewerList = async () => {
  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 10,
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    newerList.value = newItems
  }
}

// 获取爆款好物列表
const getHotProductsList = async (isLoadMore = false) => {
  if (hotProductsLoading.value) return

  hotProductsLoading.value = true

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: hotProductsCurrentPage.value,
    page_size: hotProductsPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式为GoodsWaterfallItem组件需要的格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    if (isLoadMore) {
      hotProductsList.value = [...hotProductsList.value, ...newItems]
    } else {
      hotProductsList.value = newItems
    }

    // 只有当返回的数据为空数组时才停止加载
    if (json.length === 0) {
      hotProductsFinished.value = true
    }

    // 只有在成功加载数据时才递增页码
    if (isLoadMore) {
      hotProductsCurrentPage.value++
    } else {
      // 首次加载成功后，将页码设置为2，为下次加载做准备
      hotProductsCurrentPage.value = 2
    }

    // 等待DOM更新完成，确保Waterfall渲染完毕
    await nextTick()

    // 给Waterfall更多时间来渲染和计算布局，特别是首次加载
    if (!isLoadMore) {
      // 首次加载后额外等待，确保瀑布流完全渲染
      await new Promise(resolve => setTimeout(resolve, 500))
      hotProductsIsFirstLoadComplete.value = true
    } else {
      // 后续加载稍微等待一下
      await new Promise(resolve => setTimeout(resolve, 200))
    }
  } else {
    // 没有数据或出错时，标记为加载完成
    hotProductsFinished.value = true
  }

  hotProductsLoading.value = false
}

// 爆款好物加载更多处理
const handleHotProductsLoadMore = () => {
  const now = Date.now()
  if (!hotProductsFinished.value && !hotProductsLoading.value && hotProductsIsFirstLoadComplete.value && (now - hotProductsLastLoadTime.value > 1000)) {
    hotProductsLastLoadTime.value = now
    getHotProductsList(true)
  }
}

const handleGoodsClick = (goodsInfo) => {
  console.log('点击商品:', goodsInfo)
  if (goodsInfo.goodsId) {
    // 跳转到商品详情页
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}



// 组件挂载时获取数据
onMounted(() => {
  getHeaderBannerList()
  getIconList()
  // 加载各县销冠数据
  getLimitedList(false)
  // 加载新上好物数据
  getNewerList()
  // 加载爆款好物数据
  getHotProductsList(false)
})
</script>

<style scoped lang="less">
.bf-home {
  .banner-container {
    margin: 8px 12px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .grid-menu-container {
    margin: 8px 0;
    background: #ffffff;
    border-radius: 12px;
    margin: 8px 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .horizontal-scroll-container {
    padding: 0 12px;

    .horizontal-scroll-wrapper {
      display: flex;
      gap: 12px;
      overflow-x: auto;
      padding-bottom: 8px;
      scroll-behavior: smooth;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        display: none;
      }

      -ms-overflow-style: none;
      scrollbar-width: none;

      .goods-item {
        flex: 0 0 160px;
        cursor: pointer;

        // 最后一个元素添加右边距
        &:last-child {
          margin-right: 12px;
        }
      }
    }
  }
}
</style>
