<template>
  <div class="home-skeleton">
    <!-- 标题骨架屏 -->
    <div class="skeleton-header">
      <div class="skeleton-title"></div>
    </div>

    <!-- 内容骨架屏 -->
    <div class="skeleton-content">
      <!-- 瀑布流骨架屏 -->
      <div v-if="type === 'waterfall'" class="waterfall-skeleton">
        <div class="skeleton-grid">
          <div v-for="i in count" :key="i" class="skeleton-card">
            <div class="skeleton-image"></div>
            <div class="skeleton-info">
              <div class="skeleton-line title"></div>
              <div class="skeleton-line spec"></div>
              <div class="skeleton-line sales"></div>
              <div class="skeleton-footer">
                <div class="skeleton-line price"></div>
                <div class="skeleton-cart"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 横向滚动骨架屏 -->
      <div v-else-if="type === 'horizontal'" class="horizontal-skeleton">
        <div class="skeleton-scroll">
          <div v-for="i in count" :key="i" class="skeleton-horizontal-item">
            <div class="skeleton-image"></div>
            <div class="skeleton-info">
              <div class="skeleton-line title"></div>
              <div class="skeleton-line price"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 默认列表骨架屏 -->
      <div v-else class="list-skeleton">
        <div v-for="i in count" :key="i" class="skeleton-item">
          <div class="skeleton-image"></div>
          <div class="skeleton-info">
            <div class="skeleton-line title"></div>
            <div class="skeleton-line spec"></div>
            <div class="skeleton-footer">
              <div class="skeleton-line price"></div>
              <div class="skeleton-cart"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  // 骨架屏类型：waterfall, horizontal, list
  type: {
    type: String,
    default: 'list',
    validator: (value) => ['waterfall', 'horizontal', 'list'].includes(value)
  },
  // 骨架屏数量
  count: {
    type: Number,
    default: 4
  },
  // 标题
  title: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="less">
.home-skeleton {
  margin-top: 24px;

  .skeleton-header {
    padding: 0 16px;
    margin-bottom: 16px;

    .skeleton-title {
      width: 120px;
      height: 24px;
      border-radius: 4px;
    }
  }

  .skeleton-content {
    padding: 0 10px;
  }

  // 基础骨架屏样式
  .skeleton-line,
  .skeleton-image,
  .skeleton-cart,
  .skeleton-title {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }

  @keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  // 瀑布流骨架屏
  .waterfall-skeleton {
    .skeleton-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
    }

    .skeleton-card {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;

      .skeleton-image {
        width: 100%;
        height: 120px;
      }

      .skeleton-info {
        padding: 10px;

        .skeleton-line {
          height: 12px;
          margin-bottom: 6px;

          &.title {
            width: 90%;
          }

          &.spec {
            width: 70%;
          }

          &.sales {
            width: 50%;
          }

          &.price {
            width: 50%;
          }
        }

        .skeleton-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 8px;

          .skeleton-cart {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }

  // 横向滚动骨架屏
  .horizontal-skeleton {
    .skeleton-scroll {
      display: flex;
      gap: 12px;
      overflow: hidden;

      .skeleton-horizontal-item {
        flex: 0 0 160px;
        background: #fff;
        border-radius: 8px;
        overflow: hidden;

        .skeleton-image {
          width: 100%;
          height: 120px;
        }

        .skeleton-info {
          padding: 10px;

          .skeleton-line {
            height: 12px;
            margin-bottom: 6px;

            &.title {
              width: 90%;
            }

            &.price {
              width: 60%;
            }
          }
        }
      }
    }
  }

  // 列表骨架屏
  .list-skeleton {
    .skeleton-item {
      display: flex;
      background: #fff;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;

      .skeleton-image {
        width: 80px;
        height: 80px;
        flex-shrink: 0;
      }

      .skeleton-info {
        flex: 1;
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .skeleton-line {
          height: 12px;

          &.title {
            width: 80%;
            margin-bottom: 6px;
          }

          &.spec {
            width: 60%;
            margin-bottom: 8px;
          }

          &.price {
            width: 40%;
          }
        }

        .skeleton-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .skeleton-cart {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }
}
</style>
