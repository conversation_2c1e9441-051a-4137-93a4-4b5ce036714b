<template>
  <div class="lazy-module" ref="moduleRef">
    <!-- 骨架屏 -->
    <div v-if="!isLoaded" class="skeleton-container">
      <slot name="skeleton">
        <div class="default-skeleton">
          <div class="skeleton-header">
            <div class="skeleton-title"></div>
          </div>
          <div class="skeleton-content">
            <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
              <div class="skeleton-image"></div>
              <div class="skeleton-text">
                <div class="skeleton-line"></div>
                <div class="skeleton-line short"></div>
              </div>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 实际内容 -->
    <div v-if="isLoaded" class="module-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  // 骨架屏数量
  skeletonCount: {
    type: Number,
    default: 3
  },
  // 是否等待上一个模块加载完成
  waitForPrevious: {
    type: Boolean,
    default: true
  },
  // 提前加载的距离（像素）
  rootMargin: {
    type: String,
    default: '100px'
  },
  // 模块ID，用于顺序控制
  moduleId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['load-start', 'load-complete'])

const moduleRef = ref(null)
const isLoaded = ref(false)
const isInViewport = ref(false)

let observer = null

// 简化的加载控制
let loadingDelay = 0

// 开始加载模块
const startLoading = async () => {
  if (isLoaded.value) {
    return
  }

  console.log(`开始加载模块: ${props.moduleId}`)

  emit('load-start')

  // 如果需要等待上一个模块，添加延迟
  if (props.waitForPrevious) {
    loadingDelay += 500 // 每个模块延迟500ms
    await new Promise(resolve => setTimeout(resolve, loadingDelay))
  } else {
    // 让骨架屏显示一下
    await new Promise(resolve => setTimeout(resolve, 300))
  }

  isLoaded.value = true

  // 等待DOM更新
  await nextTick()

  emit('load-complete')

  console.log(`模块加载完成: ${props.moduleId}`)
}

// 初始化观察器
const initObserver = () => {
  if (!moduleRef.value) return

  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isInViewport.value = true
          startLoading()
          // 停止观察，避免重复触发
          observer.unobserve(entry.target)
        }
      })
    },
    {
      rootMargin: props.rootMargin,
      threshold: 0.1
    }
  )

  observer.observe(moduleRef.value)
}

onMounted(() => {
  initObserver()
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>

<style scoped lang="less">
.lazy-module {
  min-height: 200px;
}

.skeleton-container {
  .default-skeleton {
    padding: 16px;

    .skeleton-header {
      margin-bottom: 16px;

      .skeleton-title {
        width: 120px;
        height: 24px;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4px;
      }
    }

    .skeleton-content {
      .skeleton-item {
        display: flex;
        margin-bottom: 12px;
        background: #fff;
        border-radius: 8px;
        padding: 12px;

        .skeleton-image {
          width: 80px;
          height: 80px;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4px;
          flex-shrink: 0;
        }

        .skeleton-text {
          flex: 1;
          margin-left: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .skeleton-line {
            height: 14px;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 8px;

            &.short {
              width: 60%;
            }
          }
        }
      }
    }
  }

  @keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
}

.module-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
