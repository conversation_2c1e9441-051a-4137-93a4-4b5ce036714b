# LazyModule 懒加载组件使用说明

## 概述

LazyModule 是一个简单易用的模块懒加载组件，支持视口检测、骨架屏显示和顺序加载控制。

## 特性

- ✅ **视口检测**：只有当模块进入视口时才开始加载
- ✅ **骨架屏支持**：加载过程中显示骨架屏，提升用户体验
- ✅ **顺序加载**：可控制模块按顺序加载，避免同时加载多个模块
- ✅ **简单易用**：只需包装现有组件即可
- ✅ **性能优化**：减少初始页面加载时间，提升性能

## 基本用法

```vue
<template>
  <LazyModule 
    module-id="my-module" 
    @load-start="handleLoadStart"
    @load-complete="handleLoadComplete">
    
    <!-- 自定义骨架屏 -->
    <template #skeleton>
      <HomeSkeleton type="waterfall" :count="4" title="模块标题" />
    </template>
    
    <!-- 实际内容 -->
    <div>
      <h2>我的模块内容</h2>
      <!-- 你的组件内容 -->
    </div>
  </LazyModule>
</template>

<script setup>
import LazyModule from '@/components/Common/LazyModule.vue'
import HomeSkeleton from '@/components/Common/Home/HomeSkeleton.vue'

const handleLoadStart = () => {
  console.log('开始加载模块')
  // 在这里执行数据加载逻辑
  loadModuleData()
}

const handleLoadComplete = () => {
  console.log('模块加载完成')
}

const loadModuleData = async () => {
  // 你的数据加载逻辑
}
</script>
```

## 组件属性

### LazyModule Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `module-id` | String | 必填 | 模块唯一标识符，用于顺序控制 |
| `skeleton-count` | Number | 3 | 默认骨架屏数量 |
| `wait-for-previous` | Boolean | true | 是否等待上一个模块加载完成 |
| `root-margin` | String | '100px' | 提前加载的距离 |

### LazyModule Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `load-start` | 开始加载时触发 | - |
| `load-complete` | 加载完成时触发 | - |

### HomeSkeleton Props

| 属性名 | 类型 | 默认值 | 可选值 | 说明 |
|--------|------|--------|--------|------|
| `type` | String | 'list' | 'waterfall', 'horizontal', 'list' | 骨架屏类型 |
| `count` | Number | 4 | - | 骨架屏项目数量 |
| `title` | String | '' | - | 模块标题 |

## 使用场景

### 1. 瀑布流模块

```vue
<LazyModule module-id="waterfall-module">
  <template #skeleton>
    <HomeSkeleton type="waterfall" :count="6" title="商品列表" />
  </template>
  
  <Block title="商品列表">
    <Waterfall :list="goodsList" :breakpoints="breakpoints">
      <template #default="{ item }">
        <GoodsCard :goods-info="item" />
      </template>
    </Waterfall>
  </Block>
</LazyModule>
```

### 2. 横向滚动模块

```vue
<LazyModule module-id="horizontal-module">
  <template #skeleton>
    <HomeSkeleton type="horizontal" :count="5" title="推荐商品" />
  </template>
  
  <Block title="推荐商品">
    <div class="horizontal-scroll">
      <div v-for="item in recommendList" :key="item.id">
        <GoodsCard :goods-info="item" />
      </div>
    </div>
  </Block>
</LazyModule>
```

### 3. 顺序加载多个模块

```vue
<template>
  <!-- 第一个模块：立即加载 -->
  <LazyModule 
    module-id="module-1" 
    :wait-for-previous="false"
    @load-start="loadModule1">
    <template #skeleton>
      <HomeSkeleton type="waterfall" :count="4" />
    </template>
    <!-- 模块1内容 -->
  </LazyModule>

  <!-- 第二个模块：等待第一个完成 -->
  <LazyModule 
    module-id="module-2" 
    :wait-for-previous="true"
    @load-start="loadModule2">
    <template #skeleton>
      <HomeSkeleton type="horizontal" :count="5" />
    </template>
    <!-- 模块2内容 -->
  </LazyModule>

  <!-- 第三个模块：等待第二个完成 -->
  <LazyModule 
    module-id="module-3" 
    :wait-for-previous="true"
    @load-start="loadModule3">
    <template #skeleton>
      <HomeSkeleton type="list" :count="3" />
    </template>
    <!-- 模块3内容 -->
  </LazyModule>
</template>
```

## 自定义骨架屏

如果内置的骨架屏不满足需求，可以自定义：

```vue
<LazyModule module-id="custom-module">
  <template #skeleton>
    <div class="custom-skeleton">
      <div class="skeleton-header">
        <div class="skeleton-title"></div>
      </div>
      <div class="skeleton-content">
        <!-- 自定义骨架屏内容 -->
      </div>
    </div>
  </template>
  
  <!-- 实际内容 -->
</LazyModule>

<style>
.custom-skeleton .skeleton-title {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  height: 20px;
  width: 150px;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
</style>
```

## 最佳实践

1. **合理设置 module-id**：确保每个模块有唯一的ID
2. **控制加载顺序**：重要模块设置 `wait-for-previous="false"`，次要模块设置为 `true`
3. **调整提前加载距离**：根据网络情况调整 `root-margin`
4. **选择合适的骨架屏**：根据内容类型选择对应的骨架屏类型
5. **监听加载事件**：在 `load-start` 事件中执行数据加载逻辑

## 注意事项

- 确保每个 LazyModule 都有唯一的 `module-id`
- 数据加载逻辑应该在 `load-start` 事件中执行
- 如果不需要顺序加载，可以设置 `wait-for-previous="false"`
- 骨架屏会在模块加载完成后自动隐藏
